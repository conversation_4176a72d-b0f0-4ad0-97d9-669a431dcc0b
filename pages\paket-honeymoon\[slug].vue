<template>
  <div>
    <main>
      <!-- Loading State -->
      <div v-if="isLoading" class="container mx-auto px-4 py-16">
        <div class="text-center">
          <div class="inline-block animate-spin rounded-full h-8 w-8 border-4 border-pink-600 border-t-transparent"></div>
          <p class="text-lg text-gray-600 mt-4">Memuat detail paket honeymoon...</p>
        </div>
      </div>

      <!-- Error State -->
      <div v-else-if="error" class="container mx-auto px-4 py-16">
        <div class="text-center">
          <p class="text-lg text-red-600">{{ error }}</p>
          <NuxtLink to="/paket-honeymoon" class="mt-4 inline-block bg-pink-600 text-white px-6 py-3 rounded-lg hover:bg-pink-700 transition-colors">
            Kembali ke Paket Honeymoon
          </NuxtLink>
        </div>
      </div>

      <!-- Detail Page -->
      <div v-else-if="paket" class="bg-white">
        <!-- Breadcrumb -->
        <div class="bg-gray-50 py-4">
          <div class="container mx-auto px-4">
            <nav class="flex items-center space-x-2 text-sm">
              <NuxtLink to="/" class="text-gray-500 hover:text-gray-700">Home</NuxtLink>
              <span class="text-gray-400">/</span>
              <NuxtLink to="/paket-honeymoon" class="text-gray-500 hover:text-gray-700">Paket Honeymoon</NuxtLink>
              <span class="text-gray-400">/</span>
              <span class="text-gray-900 font-medium">{{ paket.title }}</span>
            </nav>
          </div>
        </div>

        <!-- Hero Section -->
        <div class="container mx-auto px-4 pt-8">
          <div class="flex items-start justify-between mb-6">
            <div class="flex-1">
              <h1 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                {{ paket.title }}
              </h1>
              <div class="flex flex-wrap items-center gap-6 text-sm">
                <div class="flex items-center gap-1">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                  </svg>
                  <span class="text-gray-700">{{ paket.location }}</span>
                </div>
                <div class="flex items-center gap-1">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                  </svg>
                  <span class="text-gray-700">{{ paket.rating }} ({{ paket.reviews }} ulasan)</span>
                </div>
                <div class="flex items-center gap-1">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  <span class="text-gray-700">{{ paket.duration }}</span>
                </div>
              </div>
            </div>
            <div class="flex gap-3 ml-6">
              <button class="flex items-center gap-2 px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.368 2.684 3 3 0 00-5.368-2.684z" />
                </svg>
                Share
              </button>
              <button class="flex items-center gap-2 px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                </svg>
                Save
              </button>
            </div>
          </div>

          <!-- Image Gallery -->
          <ImageGallery :images="paket.images" :title="paket.title" />
        </div>

        <!-- Content Section -->
        <div class="container mx-auto px-4 py-8">
          <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Main Content -->
            <div class="lg:col-span-2 space-y-8">
              <!-- Description -->
              <div class="bg-white rounded-xl shadow-sm p-6 md:p-8">
                <h2 class="text-2xl font-bold mb-4">Tentang Paket Honeymoon Ini</h2>
                <p class="text-gray-600 mb-6 leading-relaxed">{{ paket.description }}</p>

                <!-- Highlights -->
                <h3 class="text-xl font-bold mb-4">Highlight Paket</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
                  <div v-for="(highlight, index) in paket.highlights" :key="index" 
                       class="flex items-start gap-3 text-gray-600">
                    <svg class="w-5 h-5 text-pink-500 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                      <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                    </svg>
                    {{ highlight }}
                  </div>
                </div>
              </div>

              <!-- Itinerary -->
              <div class="bg-white rounded-xl shadow-sm p-6 md:p-8">
                <h3 class="text-xl font-bold mb-6">Itinerary Perjalanan</h3>
                <div class="space-y-6">
                  <div v-for="day in paket.itinerary" :key="day.day" class="border-l-4 border-pink-500 pl-6">
                    <h4 class="text-lg font-semibold text-gray-900 mb-3">{{ day.title }}</h4>
                    <ul class="space-y-2">
                      <li v-for="(activity, index) in day.activities" :key="index" 
                          class="flex items-start gap-2 text-gray-600">
                        <span class="w-2 h-2 bg-pink-500 rounded-full mt-2 flex-shrink-0"></span>
                        {{ activity }}
                      </li>
                    </ul>
                  </div>
                </div>
              </div>

              <!-- Inclusions & Exclusions -->
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div class="bg-green-50 rounded-xl p-6">
                  <h3 class="text-lg font-bold text-green-800 mb-4 flex items-center gap-2">
                    <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                      <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                    </svg>
                    Termasuk dalam Paket
                  </h3>
                  <ul class="space-y-2">
                    <li v-for="(inclusion, index) in paket.inclusions" :key="index" 
                        class="text-sm text-green-700 flex items-start gap-2">
                      <svg class="w-4 h-4 text-green-600 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                      </svg>
                      {{ inclusion }}
                    </li>
                  </ul>
                </div>

                <div class="bg-red-50 rounded-xl p-6">
                  <h3 class="text-lg font-bold text-red-800 mb-4 flex items-center gap-2">
                    <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                      <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"/>
                    </svg>
                    Tidak Termasuk
                  </h3>
                  <ul class="space-y-2">
                    <li v-for="(exclusion, index) in paket.exclusions" :key="index" 
                        class="text-sm text-red-700 flex items-start gap-2">
                      <svg class="w-4 h-4 text-red-600 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"/>
                      </svg>
                      {{ exclusion }}
                    </li>
                  </ul>
                </div>
              </div>
            </div>

            <!-- Sidebar -->
            <div class="lg:col-span-1">
              <div class="bg-white rounded-xl shadow-lg p-6 sticky top-8">
                <!-- Price Section -->
                <div class="mb-6">
                  <div class="flex items-center justify-between mb-2">
                    <span class="text-gray-500 text-sm">Harga paket</span>
                    <div v-if="paket.originalPrice" class="text-right">
                      <div class="text-sm text-gray-500 line-through">{{ paket.originalPrice }}</div>
                      <div class="text-xs text-green-600 font-medium">Hemat {{ calculateDiscount(paket.price, paket.originalPrice) }}%</div>
                    </div>
                  </div>
                  <div class="text-3xl font-bold text-pink-600">{{ paket.price }}</div>
                  <div class="text-sm text-gray-500">per pasangan</div>
                </div>

                <!-- Booking Buttons -->
                <div class="space-y-3 mb-6">
                  <button 
                    @click="openWhatsApp"
                    class="w-full bg-pink-600 text-white py-3 px-6 rounded-lg font-medium hover:bg-pink-700 transition-colors"
                  >
                    Pesan Sekarang
                  </button>
                  <NuxtLink 
                    to="/kontak"
                    class="w-full border-2 border-pink-600 text-pink-600 py-3 px-6 rounded-lg font-medium hover:bg-pink-50 transition-colors text-center block"
                  >
                    Konsultasi Gratis
                  </NuxtLink>
                </div>

                <!-- Facilities -->
                <div class="border-t border-gray-200 pt-6">
                  <h3 class="font-bold mb-4">Fasilitas Unggulan</h3>
                  <div class="space-y-3">
                    <div v-for="(facility, index) in paket.facilities" :key="index" 
                         class="flex items-center gap-3 text-gray-600">
                      <svg class="w-5 h-5 text-pink-500 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                      </svg>
                      <span class="text-sm">{{ facility }}</span>
                    </div>
                  </div>
                </div>

                <!-- Contact Info -->
                <div class="border-t border-gray-200 pt-6 mt-6">
                  <div class="text-center">
                    <p class="text-sm text-gray-600 mb-2">Butuh bantuan?</p>
                    <a href="tel:+6281234567890" class="text-pink-600 font-medium hover:text-pink-700">
                      +62 812-3456-7890
                    </a>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </main>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRoute, useHead } from '#imports'
import { honeymoonPackages } from '~/data/honeymoon.js'
import ImageGallery from '~/components/ImageGallery.vue'

const route = useRoute()

// State
const isLoading = ref(true)
const error = ref(null)

// Get current package
const paket = computed(() => {
  const slug = route.params.slug
  return honeymoonPackages.find(item => item.slug === slug)
})

// Calculate discount percentage
const calculateDiscount = (currentPrice, originalPrice) => {
  const current = parseInt(currentPrice.replace(/[^\d]/g, ''))
  const original = parseInt(originalPrice.replace(/[^\d]/g, ''))
  return Math.round(((original - current) / original) * 100)
}

// Open WhatsApp
const openWhatsApp = () => {
  if (!paket.value) return
  const message = `Halo, saya tertarik dengan ${paket.value.title}. Bisa minta informasi lebih detail dan cara pemesanannya?`
  const whatsappUrl = `https://wa.me/6281234567890?text=${encodeURIComponent(message)}`
  window.open(whatsappUrl, '_blank')
}

// Initialize page
onMounted(() => {
  console.log('Honeymoon detail page mounted, slug:', route.params.slug)
  console.log('Package:', paket.value)
  
  if (!paket.value) {
    error.value = 'Paket honeymoon tidak ditemukan'
  }
  
  isLoading.value = false
})

// SEO metadata
useHead(() => ({
  title: paket.value ? `${paket.value.title} | Travelo` : 'Paket Honeymoon Tidak Ditemukan',
  meta: [
    { name: 'description', content: paket.value?.description || 'Paket honeymoon tidak ditemukan' },
    { name: 'keywords', content: `${paket.value?.title}, honeymoon jogja, paket bulan madu, ${paket.value?.location}` },
    // Open Graph / Facebook
    { property: 'og:type', content: 'product' },
    { property: 'og:url', content: `https://travelo.com/paket-honeymoon/${route.params.slug}` },
    { property: 'og:title', content: paket.value?.title || 'Paket Honeymoon Tidak Ditemukan' },
    { property: 'og:description', content: paket.value?.description || 'Paket honeymoon tidak ditemukan' },
    { property: 'og:image', content: paket.value?.image || '' },
    // Twitter
    { name: 'twitter:card', content: 'summary_large_image' },
    { name: 'twitter:url', content: `https://travelo.com/paket-honeymoon/${route.params.slug}` },
    { name: 'twitter:title', content: paket.value?.title || 'Paket Honeymoon Tidak Ditemukan' },
    { name: 'twitter:description', content: paket.value?.description || 'Paket honeymoon tidak ditemukan' },
    { name: 'twitter:image', content: paket.value?.image || '' }
  ],
  link: [
    { rel: 'canonical', href: `https://travelo.com/paket-honeymoon/${route.params.slug}` }
  ]
}))

// Structured Data for SEO (using head meta)
useHead(() => ({
  script: paket.value ? [
    {
      type: 'application/ld+json',
      children: JSON.stringify({
        '@context': 'https://schema.org',
        '@type': 'Product',
        name: paket.value.title,
        description: paket.value.description,
        image: paket.value.images,
        offers: {
          '@type': 'Offer',
          price: paket.value.price.replace(/[^\d]/g, ''),
          priceCurrency: 'IDR',
          availability: 'https://schema.org/InStock'
        },
        aggregateRating: {
          '@type': 'AggregateRating',
          ratingValue: paket.value.rating,
          reviewCount: paket.value.reviews
        }
      })
    }
  ] : []
}))
</script>
