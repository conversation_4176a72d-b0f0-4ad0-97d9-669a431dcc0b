// generated by the @nuxtjs/tailwindcss <https://github.com/nuxt-modules/tailwindcss> module at 18/06/2025, 14.32.50
import "@nuxtjs/tailwindcss/config-ctx"
import configMerger from "@nuxtjs/tailwindcss/merger";

import cfg2 from "./../../tailwind.config.js";
const config = [
{"content":{"files":["E:/1.web bisnis/bisnis-travel/website-tour-travel-nuxt/components/**/*.{vue,js,jsx,mjs,ts,tsx}","E:/1.web bisnis/bisnis-travel/website-tour-travel-nuxt/components/global/**/*.{vue,js,jsx,mjs,ts,tsx}","E:/1.web bisnis/bisnis-travel/website-tour-travel-nuxt/components/**/*.{vue,js,jsx,mjs,ts,tsx}","E:/1.web bisnis/bisnis-travel/website-tour-travel-nuxt/layouts/**/*.{vue,js,jsx,mjs,ts,tsx}","E:/1.web bisnis/bisnis-travel/website-tour-travel-nuxt/plugins/**/*.{js,ts,mjs}","E:/1.web bisnis/bisnis-travel/website-tour-travel-nuxt/composables/**/*.{js,ts,mjs}","E:/1.web bisnis/bisnis-travel/website-tour-travel-nuxt/utils/**/*.{js,ts,mjs}","E:/1.web bisnis/bisnis-travel/website-tour-travel-nuxt/pages/**/*.{vue,js,jsx,mjs,ts,tsx}","E:/1.web bisnis/bisnis-travel/website-tour-travel-nuxt/{A,a}pp.{vue,js,jsx,mjs,ts,tsx}","E:/1.web bisnis/bisnis-travel/website-tour-travel-nuxt/{E,e}rror.{vue,js,jsx,mjs,ts,tsx}","E:/1.web bisnis/bisnis-travel/website-tour-travel-nuxt/app.config.{js,ts,mjs}"]}},
{},
cfg2
].reduce((acc, curr) => configMerger(acc, curr), {});

const resolvedConfig = (() => {const cfg=config;cfg["content"]["files"]["11"] = "E:/1.web bisnis/bisnis-travel/website-tour-travel-nuxt/.nuxt/content-cache/parsed/**/*.{md,yml,yaml,json}";;return cfg;})();

export default resolvedConfig;