<template>
  <div class="bg-white rounded-2xl shadow-xl overflow-hidden hover:shadow-2xl transition-all duration-300 group">
    <!-- Image Section -->
    <div class="relative h-64 overflow-hidden">
      <img 
        :src="paket.image" 
        :alt="paket.title"
        class="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500"
      />
      <div class="absolute inset-0 bg-gradient-to-t from-black/50 via-transparent to-transparent"></div>
      
      <!-- Price Badge -->
      <div class="absolute top-4 right-4 bg-pink-600 text-white px-4 py-2 rounded-full">
        <div class="text-sm font-medium"><PERSON><PERSON> dari</div>
        <div class="text-lg font-bold">{{ paket.price }}</div>
      </div>

      <!-- Discount Badge -->
      <div v-if="paket.originalPrice" class="absolute top-4 left-4 bg-red-500 text-white px-3 py-1 rounded-full text-sm font-medium">
        Hemat {{ calculateDiscount(paket.price, paket.originalPrice) }}%
      </div>

      <!-- Duration Badge -->
      <div class="absolute bottom-4 left-4 bg-white/90 backdrop-blur-sm text-gray-800 px-3 py-1 rounded-full text-sm font-medium">
        {{ paket.duration }}
      </div>
    </div>

    <!-- Content Section -->
    <div class="p-6">
      <!-- Title and Rating -->
      <div class="mb-4">
        <h3 class="text-xl font-bold text-gray-900 mb-2 line-clamp-2">{{ paket.title }}</h3>
        <div class="flex items-center gap-2">
          <div class="flex items-center">
            <svg v-for="i in 5" :key="i" 
                 :class="i <= paket.rating ? 'text-yellow-400' : 'text-gray-300'"
                 class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
              <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
            </svg>
          </div>
          <span class="text-sm font-medium text-gray-700">{{ paket.rating }}</span>
          <span class="text-sm text-gray-500">({{ paket.reviews }} ulasan)</span>
        </div>
      </div>

      <!-- Description -->
      <p class="text-gray-600 mb-4 line-clamp-3">{{ paket.description }}</p>

      <!-- Highlights -->
      <div class="mb-6">
        <h4 class="text-sm font-semibold text-gray-900 mb-2">Highlight Paket:</h4>
        <div class="grid grid-cols-1 gap-1">
          <div v-for="(highlight, index) in paket.highlights.slice(0, 4)" :key="index" 
               class="flex items-center text-sm text-gray-600">
            <svg class="w-4 h-4 text-pink-500 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
            </svg>
            {{ highlight }}
          </div>
          <div v-if="paket.highlights.length > 4" class="text-sm text-pink-600 font-medium">
            +{{ paket.highlights.length - 4 }} fasilitas lainnya
          </div>
        </div>
      </div>

      <!-- Price Comparison -->
      <div v-if="paket.originalPrice" class="mb-4 p-3 bg-pink-50 rounded-lg">
        <div class="flex items-center justify-between">
          <div>
            <div class="text-sm text-gray-500 line-through">{{ paket.originalPrice }}</div>
            <div class="text-lg font-bold text-pink-600">{{ paket.price }}</div>
          </div>
          <div class="text-right">
            <div class="text-sm text-gray-600">Hemat</div>
            <div class="text-lg font-bold text-green-600">
              Rp {{ formatPrice(calculateSavings(paket.price, paket.originalPrice)) }}
            </div>
          </div>
        </div>
      </div>

      <!-- Action Buttons -->
      <div class="flex gap-3">
        <NuxtLink 
          :to="`/paket-honeymoon/${paket.slug}`"
          class="flex-1 bg-pink-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-pink-700 transition-colors text-center"
        >
          Lihat Detail
        </NuxtLink>
        <button 
          @click="openWhatsApp"
          class="flex-shrink-0 border-2 border-pink-600 text-pink-600 py-3 px-4 rounded-lg font-medium hover:bg-pink-50 transition-colors"
        >
          <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
            <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.109"/>
          </svg>
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
const props = defineProps({
  paket: {
    type: Object,
    required: true
  }
})

// Calculate discount percentage
const calculateDiscount = (currentPrice, originalPrice) => {
  const current = parseInt(currentPrice.replace(/[^\d]/g, ''))
  const original = parseInt(originalPrice.replace(/[^\d]/g, ''))
  return Math.round(((original - current) / original) * 100)
}

// Calculate savings amount
const calculateSavings = (currentPrice, originalPrice) => {
  const current = parseInt(currentPrice.replace(/[^\d]/g, ''))
  const original = parseInt(originalPrice.replace(/[^\d]/g, ''))
  return original - current
}

// Format price for display
const formatPrice = (amount) => {
  return new Intl.NumberFormat('id-ID').format(amount)
}

// Open WhatsApp
const openWhatsApp = () => {
  const message = `Halo, saya tertarik dengan ${props.paket.title}. Bisa minta informasi lebih detail?`
  const whatsappUrl = `https://wa.me/6281234567890?text=${encodeURIComponent(message)}`
  window.open(whatsappUrl, '_blank')
}
</script>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
