<template>
  <header 
    class="sticky top-0 z-50 transition-all duration-300"
    :class="[
      isTransparent ? 'bg-transparent' : 'bg-white shadow-sm', 
      atTop ? 'py-6' : 'py-3'
    ]"
  >
    <div class="container">
      <div class="flex items-center justify-between">
        <NuxtLink to="/" class="flex items-center gap-2">
          <img src="" alt="Travelo Logo" class="h-8" />
        </NuxtLink>
        
        <nav class="hidden md:flex items-center gap-8">
          <NuxtLink
            to="/paket-wisata-jogja"
            class="font-medium transition-colors"
            :class="isTransparent ? 'text-white hover:text-white/80' : 'text-gray-800 hover:text-primary'"
          >
            Paket Wisata Jogja
          </NuxtLink>
          <NuxtLink
            to="/paket-honeymoon"
            class="font-medium transition-colors"
            :class="isTransparent ? 'text-white hover:text-white/80' : 'text-gray-800 hover:text-primary'"
          >
            Paket Honeymoon
          </NuxtLink>
          <NuxtLink
            to="/tentang-kami"
            class="font-medium transition-colors"
            :class="isTransparent ? 'text-white hover:text-white/80' : 'text-gray-800 hover:text-primary'"
          >
            Tentang Kami
          </NuxtLink>
          <NuxtLink
            to="/kontak"
            class="font-medium transition-colors"
            :class="isTransparent ? 'text-white hover:text-white/80' : 'text-gray-800 hover:text-primary'"
          >
            Kontak
          </NuxtLink>
          <NuxtLink
            to="/blog"
            class="font-medium transition-colors"
            :class="isTransparent ? 'text-white hover:text-white/80' : 'text-gray-800 hover:text-primary'"
          >
            Blog
          </NuxtLink>
        </nav>
        
        <div class="flex items-center gap-4">
          <button 
            class="hidden md:block font-medium px-4 py-2 rounded-lg transition-colors"
            :class="isTransparent ? 'text-white border border-white hover:bg-white/20' : 'text-primary border border-primary hover:bg-primary/10'"
          >
            Sign In
          </button>
          <button 
            class="font-medium px-4 py-2 rounded-lg"
            :class="isTransparent ? 'bg-white text-primary hover:bg-white/90' : 'bg-primary text-white hover:bg-primary-dark'"
          >
            Sign Up
          </button>
          <button 
            class="md:hidden"
            :class="isTransparent ? 'text-white' : 'text-gray-800'"
            @click="toggleMobileMenu"
          >
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-6 h-6">
              <path stroke-linecap="round" stroke-linejoin="round" d="M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25h16.5" />
            </svg>
          </button>
        </div>
      </div>
    </div>
    
    <div v-if="mobileMenuOpen" class="md:hidden bg-white py-4 shadow-md">
      <div class="container">
        <nav class="flex flex-col gap-4">
          <NuxtLink to="/paket-wisata-jogja" class="font-medium hover:text-primary transition-colors">Paket Wisata Jogja</NuxtLink>
          <NuxtLink to="/paket-honeymoon" class="font-medium hover:text-primary transition-colors">Paket Honeymoon</NuxtLink>
          <NuxtLink to="/tentang-kami" class="font-medium hover:text-primary transition-colors">Tentang Kami</NuxtLink>
          <NuxtLink to="/kontak" class="font-medium hover:text-primary transition-colors">Kontak</NuxtLink>
          <NuxtLink to="/blog" class="font-medium hover:text-primary transition-colors">Blog</NuxtLink>
        </nav>
      </div>
    </div>
  </header>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue';

// Props for flexibility
const props = defineProps({
  isTransparent: {
    type: Boolean,
    default: false
  }
});

// State
const mobileMenuOpen = ref(false);
const atTop = ref(true);

// Toggle mobile menu
const toggleMobileMenu = () => {
  mobileMenuOpen.value = !mobileMenuOpen.value;
};

// Check scroll position
const handleScroll = () => {
  atTop.value = window.scrollY < 10;
};

// Lifecycle hooks
onMounted(() => {
  handleScroll(); // Initial check
  window.addEventListener('scroll', handleScroll);
});

onUnmounted(() => {
  window.removeEventListener('scroll', handleScroll);
});
</script>
