// https://nuxt.com/docs/api/configuration/nuxt-config
export default defineNuxtConfig({
  devtools: { enabled: true },

  modules: [
    '@nuxtjs/tailwindcss',
    '@nuxt/image',
     '@nuxt/content'
  ],
  content: {
    // Nuxt Content configuration
    documentDriven: false,
    highlight: {
      theme: 'github-light'
    }
  },

  typescript: {
    strict: true,
    typeCheck: false,
    shim: false,
    tsConfig: {
      compilerOptions: {
        moduleResolution: 'Bundler'
      }
    }
  },

  app: {
    head: {
      title: 'Travelo - Discover The Best Destinations',
      meta: [
        { name: 'description', content: 'Professional travel tour website offering the best destinations around the world' }
      ],
      link: [
        { rel: 'stylesheet', href: 'https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap' }
      ]
    }
  },

  compatibilityDate: '2025-03-03'
})
