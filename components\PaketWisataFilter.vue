<template>
  <div>
    <!-- Filter Container -->
    <div class="bg-white rounded-xl shadow-sm p-5">
      <!-- <PERSON><PERSON><PERSON> Filter -->
      <div class="mb-6">
        <h3 class="text-xl font-bold mb-4">Kategori Paket Wisata</h3>
        <div class="space-y-2.5">
          <NuxtLink
            to="/paket-wisata-jogja"
            class="block px-4 py-2.5 rounded-lg text-sm transition-colors"
            :class="!activeCategory ? 'bg-primary text-white' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'"
          >
            Semua Paket
          </NuxtLink>
          <NuxtLink
            v-for="category in categories"
            :key="category.slug"
            :to="`/paket-wisata-jogja/${category.slug}`"
            class="block px-4 py-2.5 rounded-lg text-sm transition-colors"
            :class="activeCategory === category.slug ? 'bg-primary text-white' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'"
          >
            {{ category.label }}
          </NuxtLink>
        </div>
      </div>

      <!-- Divider -->
      <div class="border-t border-gray-200 my-6"></div>

      <!-- Durasi Filter -->
      <div>
        <h3 class="text-xl font-bold mb-4">Durasi Paket Wisata</h3>
        <div class="space-y-2.5">
          <NuxtLink
            v-for="duration in durations"
            :key="duration.slug"
            :to="`/paket-wisata-jogja/${duration.slug}`"
            class="block px-4 py-2.5 rounded-lg text-sm transition-colors"
            :class="activeDuration === duration.slug ? 'bg-primary text-white' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'"
          >
            {{ duration.label }}
          </NuxtLink>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { usePaketWisata } from '~/composables/usePaketWisata'

const props = defineProps({
  activeCategory: {
    type: String,
    default: ''
  },
  activeDuration: {
    type: String,
    default: ''
  }
})

const { getAllCategories, getAllDurations } = usePaketWisata()

// Get all categories and durations
const categories = getAllCategories.value
const durations = getAllDurations.value
</script>
